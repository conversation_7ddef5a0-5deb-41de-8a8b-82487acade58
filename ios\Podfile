# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

# Use React Native's script
node_require('react-native/scripts/react_native_pods.rb')

# Add this to require the permissions setup script
require_relative '../node_modules/react-native-permissions/scripts/setup'

platform :ios, '15.1'
prepare_react_native_project!

# Set up permissions
setup_permissions([
  'Camera',
  'PhotosLibrary',
  'Bluetooth',
  'FaceID',
  'LocationWhenInUse',
  'LocationAlways',
  'Notifications',
])

# React Native Firebase requires static frameworks
$RNFirebaseAsStaticFramework = true

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'GoRaqtApp' do
  config = use_native_modules!

  # Use static frameworks for React Native Firebase compatibility
  use_frameworks! :linkage => :static
  # HealthKit is automatically linked through use_native_modules!
  # No need for explicit pod reference

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # React Native Maps configuration
  # Note: react-native-maps provides multiple podspecs for different configurations
  # These are automatically managed by use_native_modules! but we specify versions for consistency

  # Google Maps SDK versions (must match react-native-maps requirements)
  pod 'GoogleMaps', '9.3.0'
  pod 'Google-Maps-iOS-Utils', '6.1.0'

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
        # Replace @import GoogleMaps; with #import <GoogleMaps/GoogleMaps.h> in specific files
        specific_files = [
          "#{Pod::Config.instance.installation_root}/Pods/Google-Maps-iOS-Utils/Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.h",
          "#{Pod::Config.instance.installation_root}/Pods/Google-Maps-iOS-Utils/Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.h",
          "#{Pod::Config.instance.installation_root}/Pods/Google-Maps-iOS-Utils/Sources/GoogleMapsUtilsObjC/include/GMUPolygon.h",
          "#{Pod::Config.instance.installation_root}/Pods/Google-Maps-iOS-Utils/Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.h",
          "#{Pod::Config.instance.installation_root}/Pods/GoogleMaps/Maps/Sources/GMSEmpty.h"
        ]

        specific_files.each do |file|
          if File.exist?(file)
            text = File.read(file)
            if text.include?("@import GoogleMaps;")
              new_text = text.gsub("@import GoogleMaps;", "#import <GoogleMaps/GoogleMaps.h>")
              File.open(file, "w") { |f| f.write(new_text) }
              puts "🔧 Patched @import in: #{file}"
            else
              puts "ℹ️ No @import GoogleMaps; found in: #{file}"
            end
          else
            puts "⚠️ File not found: #{file}"
          end
      end
  end
end
