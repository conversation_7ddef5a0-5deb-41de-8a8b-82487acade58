#!/bin/bash

# React Native Maps iOS Fix Script
# Resolves CocoaPods integration errors while preserving Firebase setup

echo "🗺️  Starting React Native Maps iOS Fix..."

# Step 1: Verify react-native-maps installation
echo "📦 Verifying react-native-maps installation..."
if [ ! -d "node_modules/react-native-maps" ]; then
    echo "❌ react-native-maps not found in node_modules"
    echo "📥 Installing react-native-maps..."
    yarn add react-native-maps
fi

# Check for required podspec files
echo "🔍 Checking podspec files..."
MAPS_DIR="node_modules/react-native-maps"
REQUIRED_PODSPECS=(
    "react-native-maps.podspec"
    "react-native-google-maps.podspec" 
    "react-native-maps-generated.podspec"
)

for podspec in "${REQUIRED_PODSPECS[@]}"; do
    if [ -f "$MAPS_DIR/$podspec" ]; then
        echo "✅ Found: $podspec"
    else
        echo "❌ Missing: $podspec"
        echo "🔄 Reinstalling react-native-maps..."
        yarn remove react-native-maps
        yarn add react-native-maps
        break
    fi
done

# Step 2: Clean iOS build artifacts (preserve Firebase config)
echo "🧹 Cleaning iOS build artifacts..."
cd ios

# Remove build artifacts but keep GoogleService-Info.plist
rm -rf build/
rm -rf Pods/
rm -f Podfile.lock

# Clean Xcode derived data
rm -rf ~/Library/Developer/Xcode/DerivedData/GoRaqtApp-*

cd ..

# Step 3: Clear React Native caches
echo "🗑️  Clearing React Native caches..."
npx react-native clean
rm -rf /tmp/metro-*
rm -rf node_modules/.cache/

# Step 4: Reinstall pods with verbose output
echo "🍎 Reinstalling iOS pods..."
cd ios

# Update pod repo first
pod repo update

# Install with verbose output to catch any issues
pod install --verbose

cd ..

# Step 5: Verify installation
echo "✅ Verifying installation..."

# Check if Podfile.lock contains react-native-maps
if grep -q "react-native-maps" ios/Podfile.lock; then
    echo "✅ react-native-maps found in Podfile.lock"
else
    echo "❌ react-native-maps not found in Podfile.lock"
    exit 1
fi

# Check if Google Maps pods are installed
if grep -q "GoogleMaps" ios/Podfile.lock; then
    echo "✅ GoogleMaps SDK found in Podfile.lock"
else
    echo "❌ GoogleMaps SDK not found in Podfile.lock"
    exit 1
fi

# Check Firebase is still configured
if grep -q "Firebase" ios/Podfile.lock; then
    echo "✅ Firebase configuration preserved"
else
    echo "⚠️  Firebase configuration may need attention"
fi

echo ""
echo "🎉 React Native Maps iOS Fix completed!"
echo ""
echo "🚀 Next steps:"
echo "1. Open Xcode and clean build folder (Cmd+Shift+K)"
echo "2. Run: yarn ios"
echo "3. Test maps functionality"
echo ""
echo "📋 Verification checklist:"
echo "- ✅ react-native-maps pods installed"
echo "- ✅ GoogleMaps SDK configured"
echo "- ✅ Firebase configuration preserved"
echo "- ✅ Static frameworks maintained"
