#!/bin/bash

# React Native Maps Diagnostic Script
# Quick check for common issues

echo "🔍 React Native Maps Diagnostic Report"
echo "======================================"

# Check package.json
echo ""
echo "📦 Package Installation:"
if grep -q "react-native-maps" package.json; then
    VERSION=$(grep "react-native-maps" package.json | sed 's/.*: *"\([^"]*\)".*/\1/')
    echo "✅ react-native-maps: $VERSION"
else
    echo "❌ react-native-maps not found in package.json"
fi

# Check node_modules
echo ""
echo "📁 Node Modules:"
if [ -d "node_modules/react-native-maps" ]; then
    echo "✅ node_modules/react-native-maps exists"
    
    # Check podspec files
    PODSPECS=(
        "react-native-maps.podspec"
        "react-native-google-maps.podspec"
        "react-native-maps-generated.podspec"
    )
    
    for podspec in "${PODSPECS[@]}"; do
        if [ -f "node_modules/react-native-maps/$podspec" ]; then
            echo "✅ $podspec exists"
        else
            echo "❌ $podspec missing"
        fi
    done
else
    echo "❌ node_modules/react-native-maps missing"
fi

# Check iOS Pods
echo ""
echo "🍎 iOS Pods Status:"
if [ -f "ios/Podfile.lock" ]; then
    echo "✅ Podfile.lock exists"
    
    # Check for react-native-maps in Podfile.lock
    if grep -q "react-native-maps" ios/Podfile.lock; then
        echo "✅ react-native-maps found in Podfile.lock"
    else
        echo "❌ react-native-maps not found in Podfile.lock"
    fi
    
    # Check for GoogleMaps
    if grep -q "GoogleMaps" ios/Podfile.lock; then
        echo "✅ GoogleMaps SDK found in Podfile.lock"
    else
        echo "❌ GoogleMaps SDK not found in Podfile.lock"
    fi
    
    # Check for Firebase (should be preserved)
    if grep -q "Firebase" ios/Podfile.lock; then
        echo "✅ Firebase configuration preserved"
    else
        echo "⚠️  Firebase configuration missing"
    fi
else
    echo "❌ Podfile.lock missing - run 'pod install'"
fi

# Check Podfile configuration
echo ""
echo "⚙️  Podfile Configuration:"
if [ -f "ios/Podfile" ]; then
    if grep -q "RNFirebaseAsStaticFramework" ios/Podfile; then
        echo "✅ Firebase static framework configured"
    else
        echo "⚠️  Firebase static framework not configured"
    fi
    
    if grep -q "use_frameworks!" ios/Podfile; then
        echo "✅ Static frameworks enabled"
    else
        echo "❌ Static frameworks not enabled"
    fi
else
    echo "❌ Podfile missing"
fi

# Check AppDelegate
echo ""
echo "📱 AppDelegate Configuration:"
if [ -f "ios/GoRaqtApp/AppDelegate.swift" ]; then
    if grep -q "GMSServices.provideAPIKey" ios/GoRaqtApp/AppDelegate.swift; then
        echo "✅ Google Maps API key configured"
    else
        echo "❌ Google Maps API key not configured"
    fi
    
    if grep -q "FirebaseApp.configure" ios/GoRaqtApp/AppDelegate.swift; then
        echo "✅ Firebase configured"
    else
        echo "❌ Firebase not configured"
    fi
else
    echo "❌ AppDelegate.swift missing"
fi

echo ""
echo "🎯 Recommendations:"
echo "=================="

# Provide specific recommendations
if [ ! -d "node_modules/react-native-maps" ]; then
    echo "1. Run: yarn add react-native-maps"
fi

if [ ! -f "ios/Podfile.lock" ] || ! grep -q "react-native-maps" ios/Podfile.lock; then
    echo "2. Run: cd ios && pod install"
fi

echo "3. If issues persist, run: ./fix-react-native-maps.sh"
echo "4. For clean rebuild: ./fix-firebase-ios.sh"

echo ""
echo "📞 Support:"
echo "==========="
echo "- React Native Maps: https://github.com/react-native-maps/react-native-maps"
echo "- Firebase Setup: ./FIREBASE_SETUP_GUIDE.md"
