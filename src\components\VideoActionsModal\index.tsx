import React, {useEffect} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, TouchableOpacity, StyleSheet, FlatList} from 'react-native';
import Modal from '@/components/Modal'; // Use your existing Modal component
import Typography from '../Typography';
import {Icon, CImage} from '@/components';
import {handleTextureError} from '@/utils/memoryManager';
import {Images} from '@/config';
import LikeAnimation from '@/components/LikeAnimation';
import useTranslation from '@/hooks/useTranslation';
// import Share from 'react-native-share';

interface VideoActionsModalProps {
  visible: boolean;
  onClose: () => void;
  videoTitle: string;
  videoThumbnail: string;
  onLike?: () => void;
  onComment?: () => void;
  onShare?: () => void;
  onReport?: () => void;
  data?: any;
  isLiked?: boolean;
}

const VideoActionsModal: React.FC<VideoActionsModalProps> = ({
  visible,
  onClose,
  videoTitle,
  videoThumbnail,
  onLike,
  onComment,
  onShare,
  onReport,
  isLiked = false,
  data,
}) => {
  const handleShare = async () => {
    try {
      // await Share.open({
      //   message: 'Check out this platform',
      //   url: 'https://www.goract.com',
      // });
    } catch (error) {
      console.log('🚀 ~ handleShare ~ error:', error);
    }
  };

  const theme = useThemeStore();
  const options = [
    {
      key: 'like',
      label: 'common.likeVideo',
      icon: (
        <LikeAnimation
          addLike={() => onLike && onLike()}
          isLiked={isLiked}
          label="Like this video"
        />
      ),
      onPress: () => onLike && onLike(),
    },
    {
      key: 'comment',
      label: 'common.leaveComment',
      icon: <Icon name="comment" size={29} color={theme.colors.white} />,
      onPress: onComment,
    },
    {
      key: 'share',
      label: 'common.shareVideo',
      icon: <Icon name="upload" size={27} color={theme.colors.white} />,
      onPress: handleShare,
    },
    {
      key: 'report',
      label: 'common.reportAbuse',
      icon: <Icon name="info" size={27} color={theme.colors.white} />,
      onPress: onReport,
    },
  ];

  const {t} = useTranslation();

  // Clean up resources when modal closes
  useEffect(() => {
    if (!visible) {
      // Handle any cleanup needed when modal closes
      try {
        // Additional cleanup if needed
      } catch (error) {
        handleTextureError();
      }
    }
  }, [visible]);

  return (
    <Modal animationType="slide" variant="bottom" visible={visible} onClose={onClose}>
      <View style={styles(theme).headerRow}>
        <CImage
          source={{uri: videoThumbnail}}
          style={styles(theme).thumbnail}
          resizeMode="cover"
          fallbackSource={Images.thumbnail}
        />
        <Typography variant="subTitle4" style={styles(theme).title}>
          {videoTitle}
        </Typography>
        <TouchableOpacity onPress={onClose} style={styles(theme).closeBtn}>
          <Icon name="close1-1" size={36} color="red" />
        </TouchableOpacity>
      </View>
      <View style={styles(theme).actionsBox}>
        <FlatList
          data={options}
          contentContainerStyle={styles(theme).flatListContainer}
          renderItem={({item}) => (
            <>
              <TouchableOpacity style={styles(theme).actionRow} onPress={item.onPress}>
                {item.icon}
                {item.key !== 'like' && (
                  <Typography variant="subtitle" style={styles(theme).actionText}>
                    {t(item.label)}
                  </Typography>
                )}
              </TouchableOpacity>
              {item.key !== 'report' && <View style={styles(theme).divider} />}
            </>
          )}
          keyExtractor={item => item.key}
        />
      </View>
    </Modal>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {},
    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
      position: 'relative',
    },
    thumbnail: {
      width: 95,
      height: 50,
    },
    title: {
      flex: 1,
      color: theme.colors.white,
      marginLeft: 20,
      maxWidth: '60%',
    },
    closeBtn: {
      position: 'absolute',
      right: 0,
      top: -20,
    },
    actionsBox: {
      backgroundColor: theme.colors.white1,
      borderRadius: 10,
      marginTop: 5,
    },
    flatListContainer: {
      padding: 20,
    },
    actionRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 18,
    },
    actionText: {
      color: theme.colors.white,
    },
    divider: {
      height: 1,
      marginVertical: 18,
      backgroundColor: theme.colors.gray,
      marginHorizontal: 4,
    },
  });

export default VideoActionsModal;
