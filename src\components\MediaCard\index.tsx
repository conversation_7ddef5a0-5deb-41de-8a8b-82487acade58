import React from 'react';
import {View, StyleSheet, TouchableOpacity, ViewStyle, StyleProp} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {CImage, Icon} from '@/components';
// import Share from 'react-native-share';
type MediaType = 'image' | 'video';

interface MediaCardProps {
  type?: MediaType;
  source: string | {uri: string};
  onLikePress?: () => void;
  onCommentPress?: () => void;
  onUploadPress?: () => void;
  textColors?: {
    speed?: string;
    hashtag?: string;
    rest?: string;
  };
  containerStyle?: StyleProp<ViewStyle>;
  showActionBar?: boolean;
  imageContainerStyle?: StyleProp<ViewStyle>;
}

const MediaCard: React.FC<MediaCardProps> = ({
  type,
  source,
  onLikePress,
  onCommentPress,
  onUploadPress,
  containerStyle,
  showActionBar = true,
  imageContainerStyle,
}) => {
  const theme = useThemeStore();

  const shandleShare = async () => {
    try {
      // await Share.open({
      //   message: 'Check out this platform',
      //   url: 'https://www.goraqt.com/',
      // });
    } catch (error) {
      console.log('🚀 ~ shandleShare ~ error:', error);
    }
  };
  return (
    <View style={[styles(theme).headerContainer, containerStyle]}>
      <View style={[styles(theme).imageContainer, imageContainerStyle]}>
        <CImage resizeMode="cover" source={source} style={{height: '100%', width: '100%'}} />
      </View>
      {showActionBar && (
        <View style={styles(theme).actionContainer}>
          <TouchableOpacity activeOpacity={0.7}>
            <Icon name="like" color={theme.colors.likeRed} size={18} />
          </TouchableOpacity>
          <TouchableOpacity activeOpacity={0.7} onPress={onCommentPress}>
            <Icon name="comment" color={theme.colors.white} size={18} />
          </TouchableOpacity>
          <TouchableOpacity activeOpacity={0.7} onPress={shandleShare}>
            <Icon name="upload" color={theme.colors.white} size={18} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default MediaCard;

const styles = (theme: any) =>
  StyleSheet.create({
    headerContainer: {
      width: '100%',
      aspectRatio: 16 / 9,
    },
    actionContainer: {
      backgroundColor: theme.colors.black,
      paddingHorizontal: 30,
      paddingVertical: 5,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 15,
      flex: 1,
    },
    imageContainer: {
      width: '100%',
      height: '85%',
    },
  });
