{"name": "goraqt", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json}\"", "start": "react-native start", "test": "jest", "release": "cd ./android && ./gradlew assemblerelease", "debug": "cd ./android && ./gradlew clean && ./gradlew assembledebug", "postinstall": "patch-package"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.2", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-community/slider": "^4.5.6", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@sentry/react-native": "^6.14.0", "@tanstack/react-query": "^5.75.5", "@tanstack/react-query-devtools": "^5.75.5", "@types/react-native-push-notification": "^8.1.4", "axios": "^1.9.0", "d3-shape": "^3.2.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "i18n-js": "^4.5.1", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "lottie-react-native": "^7.2.2", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "19.0.0", "react-hook-form": "^7.55.0", "react-native": "0.79.1", "react-native-audio-recorder-player": "^3.6.12", "react-native-biometrics": "^3.0.1", "react-native-calendars": "^1.1311.1", "react-native-date-picker": "^5.0.12", "react-native-device-info": "^14.0.4", "react-native-draggable-flatlist": "^4.0.2", "react-native-element-dropdown": "^2.12.4", "react-native-fast-image": "^8.6.3", "react-native-fbsdk-next": "^13.4.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.25.0", "react-native-gifted-chat": "^2.8.1", "react-native-google-fit": "^0.21.0", "react-native-health": "^1.19.0", "react-native-image-crop-picker": "^0.50.1", "react-native-image-picker": "^8.2.1", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.22.5", "react-native-maps-directions": "^1.9.0", "react-native-mmkv": "^3.2.0", "react-native-modalize": "^2.1.1", "react-native-otp-entry": "^1.8.4", "react-native-permissions": "^5.3.0", "react-native-push-notification": "^8.1.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "^3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-render-html": "^6.3.4", "react-native-responsive-fontsize": "^0.5.1", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-shadow-2": "^7.1.1", "react-native-share": "^12.0.11", "react-native-snap-carousel": "^3.9.1", "react-native-sound-player": "^0.14.5", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.11.2", "react-native-swipe-list-view": "^3.2.9", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.13.0", "react-native-video-player": "^0.16.2", "react-native-vision-camera": "^4.6.4", "react-native-webview": "^13.13.5", "socket.io-client": "^4.8.1", "yup": "^1.6.1", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.1", "@react-native/eslint-config": "0.79.1", "@react-native/gradle-plugin": "^0.79.1", "@react-native/metro-config": "0.79.1", "@react-native/typescript-config": "0.79.1", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^29.6.3", "prettier": "^3.5.3", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}