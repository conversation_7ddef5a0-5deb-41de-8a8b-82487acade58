/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useMemo, useState} from 'react';
import {Platform, View, Text, ScrollView, Dimensions, StyleSheet} from 'react-native';
import {GooglePlacesAutocomplete} from 'react-native-google-places-autocomplete';

interface PlaceAutoCompleteProps {
  refs?: React.Ref<any>;
  isError?: boolean;
  isErrorMsg?: string;
  onAutoCompleteAddressSelect: (data: any, details: any | null) => void;
  placeholder?: string;
  onChangeText?: (text: string) => void;
  isDisable?: boolean;
  title?: string;
  mandatory?: boolean;
  location?: string;
  onFocus?: any;
  onBlur?: any;
}

const PlaceAutoComplete: React.FC<PlaceAutoCompleteProps> = ({
  refs,
  isError = false,
  isErrorMsg,
  onAutoCompleteAddressSelect,
  placeholder,
  onChangeText = () => {},
  isDisable = false,
  title,
  mandatory = false,
  location = '',
  onFocus = () => {},
  onBlur = () => {},
}) => {
  const IOS = Platform.OS === 'ios';

  // To pre-populate location text in field
  useEffect(() => {
    if (location) {
      refs?.current?.setAddressText(location);
    }
  }, [location]);

  // State to control the dropdown visibility
  const [listViewDisplayed, setListViewDisplayed] = useState(false);
  const isListViewMemo = useMemo(() => listViewDisplayed, [listViewDisplayed]);
  const dropdownBorder = isListViewMemo ? 1 : 0;

  return (
    <ScrollView
      contentContainerStyle={{zIndex: 10000, flexGrow: 1}}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps={'handled'}>
      {/* Title with mandatory indicator */}
      {title ? (
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text
            style={{
              color: 'red',
              paddingBottom: 0,
              fontSize: 16,
            }}>
            {title}
            {mandatory && <Text style={{fontSize: 15, color: 'red'}}>*</Text>}
          </Text>
        </View>
      ) : null}

      {/* Google Places Autocomplete Component */}
      <GooglePlacesAutocomplete
        ref={refs}
        placeholder={placeholder || 'addressPlaceholder'}
        query={{
          key: 'AIzaSyDWFlXKe4OeKkCrXyq7WwX39HqxvdYWZoY',
          language: 'en',
        }}
        debounce={300}
        keepResultsAfterBlur={false}
        fetchDetails
        keyboardShouldPersistTaps="always"
        listViewDisplayed={false}
        predefinedPlaces={[]}
        textInputProps={{
          editable: !isDisable,
          placeholderTextColor: 'red',
          onChangeText: (txt: any) => {
            // onChangeText(txt);
            // setListViewDisplayed(false); // Show the dropdown on typing
          },
          onFocus: () => {
            onFocus();
          },
          onBlur: () => {
            onBlur();
          },
          // defaultValue: location,
          // value: location,
        }}
        onPress={(data, details = null) => {
          console.log('data ===>', data, 'details ===>', details);
          //   // setListViewDisplayed(false); // Close dropdown
          //   onAutoCompleteAddressSelect(data, details);
          //   setListViewDisplayed(false); // Close the dropdown on selection
          //   // refs?.current?.clear(); // Optionally clear the input
        }}
        suppressDefaultStyles={true}
        styles={styles}
      />
    </ScrollView>
  );
};

export default React.memo(PlaceAutoComplete);

const styles = StyleSheet.create({
  container: {flexGrow: 1},
  listView: {
    backgroundColor: 'white',
    borderColor: '#E6EFFE',
    borderRadius: 10,
    paddingHorizontal: 10,
  },
  separator: {
    borderColor: '#E6EFFE',
    borderBottomWidth: 1,
  },
  row: {
    borderRadius: 7,
    backgroundColor: 'white',
  },
  poweredContainer: {
    display: 'none',
  },
  textInputContainer: {
    backgroundColor: 'white',
    borderRadius: 25,
    marginTop: 10,
    height: 50,
  },
  textInput: {
    backgroundColor: '#f5faff',
    fontSize: 14,
    color: 'blue',
    height: 50,
    borderWidth: 1,
    borderColor: 'transparent',
    borderRadius: 15,
    width: '90%',
    paddingHorizontal: 15,
    paddingBottom: 10,
    paddingRight: Dimensions.get('screen').width / 10,
  },
  description: {
    color: 'grey',
  },
});
