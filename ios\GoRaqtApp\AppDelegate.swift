import UIKit
import React
import React_RCTAppDelegate
import ReactAppDependencyProvider
import Firebase
import SystemConfiguration
import GoogleMaps
import UserNotifications

@main
class AppDelegate: UIResponder, UIApplicationDelegate, UNUserNotificationCenterDelegate {
  var window: UIWindow?

  var reactNativeDelegate: ReactNativeDelegate?
  var reactNativeFactory: RCTReactNativeFactory?

  func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
  ) -> Bool {
      // Set up Google Maps API key
      GMSServices.provideAPIKey("AIzaSyBYOw52zLyByyx024_iMsQl6LWgWbd9DkQ")
      // Set up Firebase
      FirebaseApp.configure()

      // Set up push notifications delegate
      UNUserNotificationCenter.current().delegate = self

      // Register for remote notifications (permissions will be handled by React Native Firebase)
      application.registerForRemoteNotifications()

      // Disable iOS network alerts by setting a custom reachability
      let reachability = SCNetworkReachabilityCreateWithName(nil, "www.apple.com")
      SCNetworkReachabilitySetCallback(reachability!, nil, nil)
      SCNetworkReachabilityScheduleWithRunLoop(reachability!, CFRunLoopGetMain(), CFRunLoopMode.commonModes.rawValue)

    let delegate = ReactNativeDelegate()
    let factory = RCTReactNativeFactory(delegate: delegate)
    delegate.dependencyProvider = RCTAppDependencyProvider()

    reactNativeDelegate = delegate
    reactNativeFactory = factory

    window = UIWindow(frame: UIScreen.main.bounds)

    factory.startReactNative(
      withModuleName: "GoRaqt",
      in: window,
      launchOptions: launchOptions
    )

    return true
  }

  // MARK: - Push Notification Delegate Methods

  // Handle APNs token registration
  func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    // React Native Firebase will automatically handle the APNs token
    print("APNs token registered successfully")
  }

  // Handle APNs token registration failure
  func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
    print("Failed to register for remote notifications: \(error)")
  }

  // MARK: - UNUserNotificationCenterDelegate

  // Called when a notification is delivered to a foreground app
  func userNotificationCenter(_ center: UNUserNotificationCenter,
                              willPresent notification: UNNotification,
                              withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
    // React Native Firebase will handle the notification logic
    // Show notification in foreground
    completionHandler([[.alert, .sound, .badge]])
  }

  // Called when user taps on notification
  func userNotificationCenter(_ center: UNUserNotificationCenter,
                              didReceive response: UNNotificationResponse,
                              withCompletionHandler completionHandler: @escaping () -> Void) {
    // React Native Firebase will handle the notification tap logic
    completionHandler()
  }


}

class ReactNativeDelegate: RCTDefaultReactNativeFactoryDelegate {
  override func sourceURL(for bridge: RCTBridge) -> URL? {
    self.bundleURL()
  }

  override func bundleURL() -> URL? {
#if DEBUG
    RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
#else
    Bundle.main.url(forResource: "main", withExtension: "jsbundle")
#endif
  }
}
