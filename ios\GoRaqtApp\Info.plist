<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>GoRaqt</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1200528734865053</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.954151296869-aeen45ptnrrt2l4ifsqibkjsovgtq3be</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAdvertiserIDCollectionEnabled</key>
	<string>FALSE</string>
	<key>FacebookAppID</key>
	<string>1200528734865053</string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<string>FALSE</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>GoRaqt</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>We need Bluetooth access to connect to devices</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>We use Bluetooth to connect to external devices</string>
	<key>NSCameraUsageDescription</key>
	<string>We need camera access to scan QR codes</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Allow GoRaqt to use Face ID to securely access your account</string>
	<key>NSHealthClinicalHealthRecordsShareUsageDescription</key>
	<string>Read and understand clinical health data.</string>
	<key>NSHealthShareUsageDescription</key>
	<string>We need access to your health data to track your steps and heart rate</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>We need permission to update your health data</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need your location for background updates.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location for better service.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We need microphone access for video recording</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need to access your photos library for change profile picture.</string>
	<key>NSUserNotificationUsageDescription</key>
	<string>We need permission to send you notifications</string>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>FontAwesome6_Brands.ttf</string>
		<string>FontAwesome6_Regular.ttf</string>
		<string>FontAwesome6_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
		<string>Helvetica-Bold.ttf</string>
		<string>helvetica-Light.ttf</string>
		<string>Helvetica.ttf</string>
		<string>icomoon.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>background-fetch</string>
		<string>remote-notification</string>
	</array>
	<key>FirebaseMessagingAutoInitEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIRequiresPersistentWiFi</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
