import {create} from 'zustand';
import {persist, createJSONStorage} from 'zustand/middleware';
import {mmkvStorage} from '@/services/storage';

// Notification types
export interface NotificationData {
  id?: string;
  title?: string;
  body?: string;
  data?: any;
  action?: string;
  meta?: string | object;
  timestamp?: number;
}

export interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  vibration: boolean;
  badge: boolean;
  inApp: boolean;
  push: boolean;
}

// Notification state interface
interface NotificationState {
  fcmToken: string | null;
  settings: NotificationSettings;
  lastNotification: NotificationData | null;
  notificationHistory: NotificationData[];
  unreadCount: number;
  isInitialized: boolean;
}

// Default notification state
const defaultNotificationState: NotificationState = {
  fcmToken: null,
  settings: {
    enabled: true,
    sound: true,
    vibration: true,
    badge: true,
    inApp: true,
    push: true,
  },
  lastNotification: null,
  notificationHistory: [],
  unreadCount: 0,
  isInitialized: false,
};

// Define the store type with actions
type NotificationStore = NotificationState & {
  // FCM Token actions
  setFcmToken: (token: string | null) => void;
  getFcmToken: () => string | null;
  clearFcmToken: () => void;

  // Notification settings actions
  updateSettings: (settings: Partial<NotificationSettings>) => void;
  toggleNotifications: () => void;
  toggleSound: () => void;
  toggleVibration: () => void;

  // Notification data actions
  setLastNotification: (notification: NotificationData) => void;
  addNotificationToHistory: (notification: NotificationData) => void;
  clearNotificationHistory: () => void;
  markAsRead: (notificationId?: string) => void;
  markAllAsRead: () => void;

  // Utility actions
  setInitialized: (initialized: boolean) => void;
  resetNotificationStore: () => void;
};

// Create the notification store with persistence
export const useNotificationStore = create<NotificationStore>()(
  persist(
    (set, get) => ({
      ...defaultNotificationState,

      // FCM Token actions
      setFcmToken: (token: string | null) => {
        console.log('Setting FCM Token:', token);
        set({fcmToken: token});
      },

      getFcmToken: () => {
        return get().fcmToken;
      },

      clearFcmToken: () => {
        set({fcmToken: null});
      },

      // Notification settings actions
      updateSettings: (newSettings: Partial<NotificationSettings>) => {
        set(state => ({
          settings: {
            ...state.settings,
            ...newSettings,
          },
        }));
      },

      toggleNotifications: () => {
        set(state => ({
          settings: {
            ...state.settings,
            enabled: !state.settings.enabled,
          },
        }));
      },

      toggleSound: () => {
        set(state => ({
          settings: {
            ...state.settings,
            sound: !state.settings.sound,
          },
        }));
      },

      toggleVibration: () => {
        set(state => ({
          settings: {
            ...state.settings,
            vibration: !state.settings.vibration,
          },
        }));
      },

      // Notification data actions
      setLastNotification: (notification: NotificationData) => {
        const notificationWithTimestamp = {
          ...notification,
          timestamp: Date.now(),
          id: notification.id || `notification_${Date.now()}`,
        };

        set(state => ({
          lastNotification: notificationWithTimestamp,
          unreadCount: state.unreadCount + 1,
        }));
      },

      addNotificationToHistory: (notification: NotificationData) => {
        const notificationWithTimestamp = {
          ...notification,
          timestamp: Date.now(),
          id: notification.id || `notification_${Date.now()}`,
        };

        set(state => ({
          notificationHistory: [notificationWithTimestamp, ...state.notificationHistory].slice(0, 100), // Keep last 100 notifications
        }));
      },

      clearNotificationHistory: () => {
        set({
          notificationHistory: [],
          unreadCount: 0,
        });
      },

      markAsRead: (notificationId?: string) => {
        if (notificationId) {
          // Mark specific notification as read (implementation depends on your needs)
          set(state => ({
            unreadCount: Math.max(0, state.unreadCount - 1),
          }));
        } else {
          // Mark last notification as read
          set(state => ({
            unreadCount: Math.max(0, state.unreadCount - 1),
          }));
        }
      },

      markAllAsRead: () => {
        set({unreadCount: 0});
      },

      // Utility actions
      setInitialized: (initialized: boolean) => {
        set({isInitialized: initialized});
      },

      resetNotificationStore: () => {
        set(defaultNotificationState);
      },
    }),
    {
      name: 'app-notifications',
      storage: createJSONStorage(() => mmkvStorage),
      // Only persist certain fields, exclude temporary data
      partialize: (state) => ({
        fcmToken: state.fcmToken,
        settings: state.settings,
        unreadCount: state.unreadCount,
        isInitialized: state.isInitialized,
        // Don't persist notification history and lastNotification for privacy/storage efficiency
      }),
    },
  ),
);
