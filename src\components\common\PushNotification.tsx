import {useEffect} from 'react';
import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';
import {useNotificationStore} from '@/store/notificationStore';
import {useAuthStore} from '@/store/authStore';
import {useConfigStore} from '@/store/configStore';
import {showNotification} from './NotificationHelper';
import {PermissionsAndroid, Platform} from 'react-native';

// const PushNotification = require('react-native-push-notification');
// Store FCM token using MMKV
const storeFCMToken = async (token: string | null) => {
  try {
    const notificationStore = useNotificationStore.getState();
    notificationStore.setFcmToken(token);
    console.log('FCM Token stored successfully:', token);
  } catch (error) {
    console.error('Error storing FCM token:', error);
  }
};

export const getToken = async () => {
  try {
    messaging()
      .getToken()
      .then(token => {
        console.log('FCM Token', token);
        return storeFCMToken(token);
      });
  } catch (e) {
    console.log('e ===>', e);
  }
};

// Delete fcm token
export const deleteFcmToken = () => {
  try {
    messaging()
      .deleteToken()
      .then(res => {
        console.log('deleteToken FCM Token', res);
        storeFCMToken('');
      });
  } catch (e) {
    console.log('e ====>', e);
  }
};

/**
 *firebase notification
 * @function  RemotePushController
 */
const RemotePushNotification = () => {
  // Use Zustand stores instead of Redux
  const notificationStore = useNotificationStore();
  const authStore = useAuthStore();
  const configStore = useConfigStore();

  // Get user data from auth store
  // TODO: Replace with proper socket store when available
  const requestNotificationPermission = async () => {
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();

      // Request permissions
      return (
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL
      );
    } else if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return false;
  };

  useEffect(() => {
    const requestPermissions = async () => {
      const hasPermission = await requestNotificationPermission();
      if (hasPermission) {
        console.log('Notification permission granted');
      } else {
        console.log('Notification permission denied');
      }
    };

    requestPermissions();
    PushNotification.configure({
      // (required) Called when a remote or local notification is opened or received
      onNotification: function (notification: any) {
        console.log('notification configure ===>', notification);
        // old app code
        // dispatch({ type: 'onNotificationOpen', payload: notification });
        // store.dispatch(NotificationAction.onNotificationOpen(notification));
        // store.dispatch(NotificationAction.setNotificationType(notification));

        //Handle on notification click Events
        if (notification?.userInteraction && notification?.foreground) {
          // callCommonHandleNotificationfunction(notification);
        }
      },

      popInitialNotification: true,
      requestPermissions: true,
    });

    PushNotification.createChannel(
      {
        channelId: '1', // (required)
        channelName: 'Goraqt', // (required)
        channelDescription: 'Goraqt default channel', // (optional) default: undefined.
        soundName: 'default', // (optional) See `soundName` parameter of `localNotification` function
        importance: 4, // (optional) default: 4. Int value of the Android notification importance
        vibrate: true, // (optional) default: true. Creates the default vibration patten if true.
      },
      created => console.log(`createChannel 'default-channel-id' returned '${created}'`), // (optional) callback returns whether the channel was created, false means it already existed.
    );

    messaging().onNotificationOpenedApp((remoteMessage: any) => {
      const {data, notification} = remoteMessage;
      const {body, title} = notification;
      console.log('remoteMessage background state: ===>', remoteMessage);
      if (data?.action) {
        // Store notification data using MMKV
        notificationStore.setLastNotification({
          title: notification?.title,
          body: notification?.body,
          data: data,
          action: data?.action,
          meta: data?.meta,
        });
      }
      console.log('Notification caused app to open from background state:', remoteMessage);
    });
    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log('Notification caused app to open from quit state:', remoteMessage);

          const {data, notification} = remoteMessage;
          console.log('remoteMessage quit state: ===>', remoteMessage);
          // callCommonHandleNotificationfunction(remoteMessage);
        }
      });

    // Get the device token
    messaging()
      .getToken()
      .then(token => {
        console.log('FCM Token', token);
        return storeFCMToken(token);
      });

    // Listen to whether the token changes
    return messaging().onTokenRefresh(token => {
      storeFCMToken(token);
    });
  }, []);
  useEffect(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      const {data} = remoteMessage;
      // Update persona status using Zustand auth store

      console.log('onMessage ===>', remoteMessage);

      showNotification(remoteMessage, true);
    });
    return unsubscribe;
  }, []);
  return null;
};

export default RemotePushNotification;
