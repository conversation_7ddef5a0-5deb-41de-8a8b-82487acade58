import {useEffect} from 'react';
import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';
import {useNotificationStore} from '@/store/notificationStore';
import {useAuthStore} from '@/store/authStore';
import {useConfigStore} from '@/store/configStore';
import {showNotification} from './NotificationHelper';
import {PermissionsAndroid, Platform} from 'react-native';

// Utility functions (replacing lodash functions)
const isEmpty = (value: any): boolean => {
  return (
    value == null || (typeof value === 'object' && Object.keys(value).length === 0) || value === ''
  );
};

const isString = (value: any): value is string => {
  return typeof value === 'string';
};

const isUndefined = (value: any): value is undefined => {
  return value === undefined;
};

// Navigation helper function
const handleNavigationOnNotification = (screenName: string, params?: any) => {
  // This function should handle navigation to specific screens
  // For now, we'll log the navigation attempt
  console.log('Navigate to:', screenName, 'with params:', params);
  // TODO: Implement proper navigation logic
};

// Navigation ref - will be set up properly when navigation is available
const navigationRef: any = null;

// const PushNotification = require('react-native-push-notification');
// Store FCM token using MMKV
const storeFCMToken = async (token: string | null) => {
  try {
    const notificationStore = useNotificationStore.getState();
    notificationStore.setFcmToken(token);
    console.log('FCM Token stored successfully:', token);
  } catch (error) {
    console.error('Error storing FCM token:', error);
  }
};

export const getToken = async () => {
  try {
    messaging()
      .getToken()
      .then(token => {
        console.log('FCM Token', token);
        return storeFCMToken(token);
      });
  } catch (e) {
    console.log('e ===>', e);
  }
};

// Delete fcm token
export const deleteFcmToken = () => {
  try {
    messaging()
      .deleteToken()
      .then(res => {
        console.log('deleteToken FCM Token', res);
        storeFCMToken('');
      });
  } catch (e) {
    console.log('e ====>', e);
  }
};

/**
 *firebase notification
 * @function  RemotePushController
 */
const RemotePushNotification = () => {
  // Use Zustand stores instead of Redux
  const notificationStore = useNotificationStore();
  const authStore = useAuthStore();
  const configStore = useConfigStore();

  // Get user data from auth store
  const userData = authStore.user;

  // TODO: Replace with proper socket store when available
  const selectedRoom: any = null; // Placeholder for socket selectedRoom
  const requestNotificationPermission = async () => {
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();

      // Request permissions
      return (
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL
      );
    } else if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return false;
  };

  useEffect(() => {
    const requestPermissions = async () => {
      const hasPermission = await requestNotificationPermission();
      if (hasPermission) {
        console.log('Notification permission granted');
      } else {
        console.log('Notification permission denied');
      }
    };

    requestPermissions();
    PushNotification.configure({
      // (required) Called when a remote or local notification is opened or received
      onNotification: function (notification: any) {
        console.log('notification configure ===>', notification);
        // old app code
        // dispatch({ type: 'onNotificationOpen', payload: notification });
        // store.dispatch(NotificationAction.onNotificationOpen(notification));
        // store.dispatch(NotificationAction.setNotificationType(notification));

        //Handle on notification click Events
        if (notification?.userInteraction && notification?.foreground) {
          // callCommonHandleNotificationfunction(notification);
        }
      },

      popInitialNotification: true,
      requestPermissions: true,
    });

    PushNotification.createChannel(
      {
        channelId: '1', // (required)
        channelName: 'harbor', // (required)
        channelDescription: 'harbor default channel', // (optional) default: undefined.
        soundName: 'default', // (optional) See `soundName` parameter of `localNotification` function
        importance: 4, // (optional) default: 4. Int value of the Android notification importance
        vibrate: true, // (optional) default: true. Creates the default vibration patten if true.
      },
      created => console.log(`createChannel 'default-channel-id' returned '${created}'`), // (optional) callback returns whether the channel was created, false means it already existed.
    );

    messaging().onNotificationOpenedApp((remoteMessage: any) => {
      const {data, notification} = remoteMessage;
      const {body, title} = notification;
      console.log('remoteMessage background state: ===>', remoteMessage);
      if (data?.action) {
        // Store notification data using MMKV
        notificationStore.setLastNotification({
          title: notification?.title,
          body: notification?.body,
          data: data,
          action: data?.action,
          meta: data?.meta,
        });

        const d = typeof data?.meta === 'string' ? JSON.parse(data?.meta) : data?.meta;

        if (d && data?.action === 'chat') {
          handleNavigationOnNotification('ChatDetails', {
            userInfo: {
              ...d,
              selectedTab: d?.receiverType,
              canGoBack: false,
            },
          });
        } else if (d?.jobId) {
          handleNavigationOnNotification('JobApplicant', d);
        }

        if (data?.action === 'update_bank_profile') {
          // Update user data using Zustand auth store
          const currentUser = authStore.user;
          if (currentUser && data?.data?.bankAccountVerified !== undefined) {
            authStore.login({
              ...currentUser,
              bankAccountVerified: data.data.bankAccountVerified,
            } as any);
          }
        }
      }
      console.log('Notification caused app to open from background state:', remoteMessage);
    });
    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log('Notification caused app to open from quit state:', remoteMessage);

          const {data, notification} = remoteMessage;
          console.log('remoteMessage quit state: ===>', remoteMessage);
          if (data?.action) {
            const d = typeof data?.meta === 'string' ? JSON.parse(data?.meta) : data?.meta;
            const obj = isString(d) ? JSON.parse(d) : d;
            if (d && data?.action === 'chat') {
              // store.dispatch(
              //   NotificationAction.onNotificationOpen({
              //     ...remoteMessage,
              //     userInfo: {...d, selectedTab: d?.receiverType},
              //     canGoBack: false,
              //   }),
              // );
              handleNavigationOnNotification('ChatDetails', {
                userInfo: {
                  ...d,
                  selectedTab: d?.receiverType,
                  canGoBack: false,
                },
              });
            } else if (d?.jobId) {
              console.log(
                'JobApplicant d ===>',
                d,
                isString(d) ? JSON.parse(d) : d,
                isString(d),
                typeof obj,
              );
              handleNavigationOnNotification('JobApplicant', {
                ...obj,
                canGoBack: false,
              });
            }
            if (!isUndefined(obj?.isOccupied)) {
              // Update user data using Zustand auth store
              const currentUser = authStore.user;
              if (currentUser) {
                authStore.login({
                  ...currentUser,
                  isOccupied: obj?.isOccupied,
                } as any);
              }
            }
          }
          // callCommonHandleNotificationfunction(remoteMessage);
        }
      });

    // Get the device token
    messaging()
      .getToken()
      .then(token => {
        console.log('FCM Token', token);
        return storeFCMToken(token);
      });

    // Listen to whether the token changes
    return messaging().onTokenRefresh(token => {
      storeFCMToken(token);
    });
  }, []);
  useEffect(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      const {data} = remoteMessage;

      // Update persona status using Zustand auth store
      if (!isEmpty(remoteMessage?.data?.personaStatus)) {
        const currentUser = authStore.user;
        if (currentUser) {
          authStore.login({
            ...currentUser,
            personaStatus: remoteMessage?.data?.personaStatus,
          } as any);
        }
      }

      // Update bank profile using Zustand auth store
      if (remoteMessage?.data?.action === 'update_bank_profile') {
        const currentUser = authStore.user;
        if (currentUser && data?.data) {
          const dataObj = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
          if (dataObj?.bankAccountVerified !== undefined) {
            authStore.login({
              ...currentUser,
              bankAccountVerified: dataObj.bankAccountVerified,
            } as any);
          }
        }
      }

      // Update availability using Zustand auth store
      if (remoteMessage?.data?.action === 'update_availability') {
        const currentUser = authStore.user;
        if (currentUser && data?.data) {
          const dataObj = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
          if (dataObj?.isAvailable !== undefined) {
            authStore.login({
              ...currentUser,
              isAvailable: dataObj.isAvailable,
            } as any);
          }
        }
      }

      if (remoteMessage?.data?.action === 'payment') {
        const currentRoute = navigationRef?.current?.getCurrentRoute();
        console.log('currentRoute ===>', currentRoute);

        // TODO: Handle payment processing modal and job data updates
        // These would need to be implemented with appropriate Zustand stores
        console.log('Payment notification received - implement with proper stores');
      }
      // handleNotification(remoteMessage);
      // dispatch({ type: 'updateNotification', payload: remoteMessage });
      // store.dispatch(NotificationAction.updateNotification(remoteMessage));
      // store.dispatch(
      //   NotificationAction.setBadgeCount(remoteMessage?.data?.badge),
      // );
      console.log('onMessage ===>', remoteMessage);

      // store.dispatch(NotificationAction.setNotificationType(remoteMessage));

      const parseAction = (action: any) => {
        if (typeof action === 'string') {
          try {
            // Attempt to parse JSON if it's a valid stringified object
            const parsed = JSON.parse(action);
            return typeof parsed === 'object' ? parsed : action;
          } catch (error) {
            // If parsing fails, return the original action as a string
            return action;
          }
        }
        return action; // Return as-is if it's already an object
      };

      const rawAction = remoteMessage?.data?.action;
      const action = parseAction(rawAction);

      if (typeof remoteMessage?.data?.meta === 'string') {
        const currentRoute = navigationRef?.current?.getCurrentRoute();
        let d: any = {};
        try {
          // Attempt to parse JSON if it's a valid stringified object
          const parsed = JSON.parse(remoteMessage?.data?.meta);
          console.log('parsed ===>', parsed, typeof parsed === 'object');
          d = typeof parsed === 'object' ? parsed : remoteMessage?.data?.meta;
        } catch (error) {
          // If parsing fails, return the original action as a string
          d = remoteMessage?.data?.meta;
        }
        console.log('remoteMessaged ===>', d);
        if (currentRoute?.name === 'JobApplicant' && d?.jobId) {
          // TODO: Implement proper event system or state management for job updates
          console.log('Job update event would be emitted:', d);
        }
      }

      // Handle specific action cases
      if (
        action?.action === 'reward' ||
        action === 'reward' ||
        action?.action === 'streakNotification'
      ) {
        // EventRegister.emit('reward_Update', remoteMessage);
      }

      const d =
        typeof remoteMessage?.data?.meta === 'string'
          ? JSON.parse(remoteMessage?.data?.meta)
          : remoteMessage?.data?.meta;

      if (action?.action === 'reward') {
        // Update user data using Zustand auth store
        const currentUser = authStore.user;
        if (currentUser && d?.currentBadge) {
          authStore.login({
            ...currentUser,
            currentBadgeName: d.currentBadge,
          } as any);
        }
      }

      // ✅ Get current active route
      const currentRoute = navigationRef?.current?.getCurrentRoute();
      const isActiveChatRoom = d?.roomId === selectedRoom?.id;
      const isChatScreenActive =
        currentRoute?.name === 'ChatDetails' &&
        isActiveChatRoom &&
        remoteMessage?.data?.action === 'chat';
      if (!isChatScreenActive) {
        showNotification(remoteMessage, true);
      } else {
        //'🔕 Notification skipped - User is in the same chat'
      }
    });
    return unsubscribe;
  }, []);
  return null;
};

export default RemotePushNotification;
